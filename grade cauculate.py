# 两学期的学分和绩点
xuefen = [
    [1.5, 3, 0.5, 0.5, 3, 1, 2, 2, 1, 1, 1.5, 5, 3],      # 第1学期
    [2,5,2,1,3,1.5,1,1,0.5,3,4]                                  # 第2学期
]
jidian = [
    [5, 4, 4, 5, 5, 5, 4, 5, 2, 4, 5, 4, 3],              # 第1学期
    [3,5,5,5,4,5,5,3,5,5,5]                                    # 第2学期
]

def calc_gpa(xuefen, jidian):
    total = sum([x * j for x, j in zip(xuefen, jidian)])
    xuefen_sum = sum(xuefen)
    return total / xuefen_sum

for i in range(2):
    gpa = calc_gpa(xuefen[i], jidian[i])
    print(f"第{i+1}学期加权绩点：{gpa:.2f}")
    