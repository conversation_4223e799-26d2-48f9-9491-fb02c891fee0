# 给你一个由若干 0 和 1 组成的字符串 s ，请你计算并返回将该字符串分割成两个 非空 子字符串（即 左 子字符串和 右 子字符串）所能获得的最大得分。
# 「分割字符串的得分」为 左 子字符串中 0 的数量加上 右 子字符串中 1 的数量。
#NOTE1 易 暴力算法：分割成两部分分别遍历左右0,1的值，然后找最大值，时间复杂度O(n^2)；
#NOTE2 两次遍历：两个子字符串当分割点为0，左边加1，右边不变;分割点为1，左边不变，右边减1.整体右移，时间复杂度O(n);

#1
class Solution:
    def maxScore(self, s: str) -> int:
        return max(s[:i].count('0') + s[i:].count('1') for i in range(1, len(s)))

#2
class Solution:
    def maxScore(self, s: str) -> int:
        ans = score = (s[0] == '0') + s[1:].count('1')#ans先初始化
        for c in s[1:-1]:#代表正数第二个到倒数第二个
            score += 1 if c == '0' else -1
            ans = max(ans, score)
        return ans

