#
# *1给定一个非负整数数组 nums，你最初位于数组的第一个下标。数组中的每个元素代表你在该位置可以跳跃的最大步长。你的目标是判断是否可以到达最后一个下标。
'''
from typing import List


class Solution:
    def canJump(self, nums: list[int]) -> bool:
        farthest = 0
        n = len(nums)
        for i in range(n):
            if farthest < i:
                return False
            farthest = max(farthest, i + nums[i])
            if farthest >= n - 1:
                return True
        return farthest >= n - 1


s = Solution()
print(s.canJump([2, 3, 1, 1, 4]))  # True
print(s.canJump([3, 2, 1, 0, 4]))  # False
'''
# *2有 N 个孩子站成一排，每个孩子都有一个评分值 ratings[i]。你需要给他们分发糖果，规则如下：每个孩子至少要获得一颗糖果；如果某个孩子的评分比相邻的孩子高（左右都要比较），那么他必须获得比相邻孩子更多的糖果。返回最少需要的糖果总数。
'''
from typing import List

class Solution:
    def candy(self, ratings: List[int]) -> int:
        n = len(ratings)
        candies = [1] * n  # 每个孩子至少一颗糖

        # 从左到右扫描：处理右边比左边高的情况
        for i in range(1, n):
            if ratings[i] > ratings[i - 1]:
                candies[i] = candies[i - 1] + 1

        # 从右到左扫描：处理左边比右边高的情况
        for i in range(n - 2, -1, -1):
            if ratings[i] > ratings[i + 1]:
                # 如果当前糖果不多于右边，就更新为右边 + 1
                candies[i] = max(candies[i], candies[i + 1] + 1)

        return sum(candies)


s = Solution()
print(s.candy([1, 0, 2]))
print(s.candy([1, 2, 2]))
print(s.candy([1, 2, 3, 4, 5]))
print(s.candy([5, 4, 3, 2, 1]))
'''
# *3以数组形式给出若干个区间的 intervals，每个区间 intervals[i] = [start, end] 表示一个区间。请你 合并所有重叠的区间，并返回一个不重叠的区间列表。

'''
class Solutions:
    def Merge_Intervals(self, intervals: list[list[int]]) -> list[list[int]]:
        if not intervals:
            return []
        intervals.sort(key=lambda x: x[0])
        # re: List[list]
        # re[0][0] = inervals[0][0]
        # re[0][1] = inervals[0][1]#NOTE错误的写法
        res = []
        res.append(intervals[0])  # NOTE正确的写法
        for start, end in intervals[1:]:
            prev_start, prev_end = res[-1]

            if start <= prev_end:
                # 可以合并
                res[-1][1] = max(prev_end, end)
            else:
                # 不能合并，加入新区间
                res.append([start, end])
        return res

s = Solutions()
print(s.Merge_Intervals([[1, 3], [2, 6], [8, 10], [15, 18]]))
print(s.Merge_Intervals([[1, 4], [4, 5]]))
#!官方题解 重点关注| 没有解包、没必要更新start、变量更少、可读性更高
class Solution:
    def merge(self, intervals: List[List[int]]) -> List[List[int]]:
        intervals.sort(key=lambda x: x[0])

        merged = []
        for interval in intervals:
            # 如果列表为空，或者当前区间与上一区间不重合，直接添加
            if not merged or merged[-1][1] < interval[0]:
                merged.append(interval)
            else:
                # 否则的话，我们就可以与上一区间进行合并
                merged[-1][1] = max(merged[-1][1], interval[1])

        return merged
'''
# *4在二维空间中有多个气球，每个气球由左右边界 [x_start, x_end] 表示。一支箭可以在 x 轴某一点射出，穿过所有经过该点的气球。你的任务是找出最少需要多少支箭可以把所有气球都打爆。


class Solutions:
    def findMinArrowShots(self, points: list[list[int]]) -> int:
        if not points:
            return 0

        # 按右边界排序
        points.sort(key=lambda x: x[1])

        arrows = 1
        arrow_pos = points[0][1]

        for x_start, x_end in points[1:]:
            if x_start > arrow_pos:
                arrows += 1
                arrow_pos = x_end

        return arrows


s = Solutions()
print(s.findMinArrowShots([[9, 12], [2, 10], [1, 6], [7, 12]]))
print(s.findMinArrowShots([[1, 2], [3, 4], [5, 6]]))
print(s.findMinArrowShots([[1, 10], [2, 9], [3, 8], [4, 7]]))

