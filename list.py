#*list是python中一种数据类型，作用：动态数组，自动扩容，支持任意类型元素，支持切片、插入、删除等操作。
#* list enumerate 字典 lambda max 
print("Good Morning!")
test=[1,2,9,2,5]
# v=[1,2]
# k="speed"

#NOTE:list append and extend，append是追加元素，extend是追加列表
# test.append(v)
# test.append(89)
# test.append("Good Night!")
# test.extend(v)
# test.extend('love')
# test.append(1223)
# test.append(k)

#NOTE:list len()函数返回列表的长度
# length1=len(test)
# length2=len(v) 
# length3=len(k)
# print(length1)
# print(length2)
# print(length3)

#NOTE:list pop()函数删除列表的最后一个元素，或者指定位置的元素
# test.pop(4)

#NOTE:list insert()函数在指定位置插入元素，在给出的位置插入，原来的自动后移，超出范围的位置自动添加到末尾
# test.insert(2, 100)
# test.insert(3, 300)
# test.insert(0, 200)
# test.insert(7, 700)
# test.insert(19, 1900)

#NOTE:list remove()函数删除指定元素，如果有多个相同元素，只删除第一个
# test.remove(2)

#NOTE:list reverse()函数反转列表
# test.reverse()

#NOTE:list sort()函数对列表进行排序，默认升序，reverse=True降序
# test.sort()
# test.sort(reverse=True)

#NOTE:list count()函数统计列表中某个元素出现的次数
# a1=test.count(1)
# test.count(1)
# print(a1)

#NOTE:del语句删除列表中指定位置的元素或指定范围的元素,[0:2]代表删除列表中前两个元素（或者是从0个位置到第1个位置，第二个位置不包括），[2]代表删除列表中第三个元素
# del test[0:2]  # 删除列表中指定范围的元素
# del test[2]   # 删除列表中指定位置的元素

#NOTE:list clear()函数清空列表
# test.clear()

#NOTE: list中元素的遍历
# for i in test:
#     print(i)

# NOTE: list的复制
# test2=test.copy()
# print(test2)

#NOTE: list的切片
# test3=test[1:3]
# print(test3)

#NOTE: list的连接
# test4=test+test2   # 连接两个列表 
# print(test4)

#NOTE: list的连接
# test5=[1,2,3]+[4,5,6]   # 连接两个列表 
# print(test5)

#NOTE: list的嵌套
# test6=[1,2,[3,4,5]]   # 列表中可以包含其他列表
# print(test6)

#*小型工程题目：学生 录入、查询、修改和统计
students = [
    ['Alice',90,99,93],
    ['Bob',85,88,86],
    [ 'Charlie',92,95,91],
    ['David',78,90,100]
]  # 用于存储学生信息的列表
def add_student(name,chinese_score,math_score,english_score):
    students.append([name,chinese_score,math_score,english_score])
def display_students():
    # for i in range(len(students)):
    #     print(students[i])
    for stu in students:
        print(stu)
#NOTE for-in遍历列表，stu是子列表，取代下标
def find_student_by_name(name):
    # bridge=0
    # for i in range(len(students)):
    #     if name==students[i][0]:
    #         print(students[i][1])
    #         print(students[i][2])
    #         print(students[i][3])
    #         bridge=1
    # if bridge==0:
    #     print("未找到该学生")
    for stu in students:
        if stu[0] == name:
            print(f"姓名: {stu[0]}, 语文: {stu[1]}, 数学: {stu[2]}, 英语: {stu[3]}")
            return#NOTE 回到for那里
    print("未找到该学生")
def update_score(name,subject,new_score):
    # for i in range(len(students)):
    #     if name==students[i][0]:
    #         if subject=='chinese':
    #             students[i][1]=new_score
    #         if subject=='math':
    #             students[i][2]=new_score
    #         if subject=='english':
    #             students[i][3]=new_score
    subject_idx = {'chinese': 1, 'math': 2, 'english': 3}#NOTE 字典映射科目到索引
    for stu in students:
        if stu[0] == name:
            stu[subject_idx[subject]] = new_score
            print(f"{name}的{subject}成绩已更新为{new_score}")
            return
    print("未找到该学生")
def delete_student(name):
    #  for i in range(len(students)):
    #     if name==students[i][0]:
    #         del students[i]
    for i, stu in enumerate(students):#NOTE enumerate()函数返回索引和元素
        #NOTE i是索引，stu是元素    
        if stu[0] == name:
            del students[i]
            print(f"{name}已被删除")
            return
    print("未找到该学生")
def calculate_average_score(subject):
    # idx = {'chinese': 1, 'math': 2, 'english': 3}[subject]，和idx = subject_idx[subject]等价
    # total_score = sum([stu[idx] for stu in students])
    # print(f"{subject}的平均分是{total_score/len(students):.2f}")
    subject_idx = {'chinese': 1, 'math': 2, 'english': 3}#NOTE 映射字典
    idx = subject_idx[subject]
    scores = [stu[idx] for stu in students]#NOTE 列表推导式，获取所有学生的指定科目成绩
    #NOTE [表达式 for 元素 in 可迭代对象 if 条件]，如果if条件成立，则将元素添加到列表中，与循环的for语句类似
    avg = sum(scores) / len(scores) if scores else 0
    #NOTE 这里scores是一个列表，存储所有学生的指定科目成绩
    print(f"{subject}的平均分是{avg:.2f}")
def find_top_student():
    # sum_score=[]
    # for i in range(len(students)):
    #     sum_score[i]=sum(students[i][1:4])
    #     if sum_score[i]>=sum_score[i-1] and i>0:
    #         k=i
    # print(f"总分最高的学生是{students[k][0]}，成绩是{max(sum_score)}")
    if not students:
        print("没有学生信息")
        return
    top_stu = max(students, key=lambda stu: sum(stu[1:4]))
    #NOTE max 函数返回列表中最大值，key参数指定一个函数，用于从每个元素中提取比较的值
    #words = ['apple', 'banana', 'pear']
    #longest = max(words, key=len)
    #print(longest)  # 输出 'banana'
    #NOTE lambda函数是一个匿名函数，lambda stu: sum(stu[1:4])表示对每个学生的成绩求和，stu是匿名函数的参数
    #NOTE stu 不是“默认”是 students 的子列表，而是 max 遍历 students 时，每次把一个子列表传给 lambda 的 stu 参数
    print(f"总分最高的学生是{top_stu[0]}，成绩是{sum(top_stu[1:4])}")
add_student('Saris',98,100,87)
add_student('Kris',94,98,81)
display_students()
find_student_by_name('Bob')
update_score('Any','math',99)
calculate_average_score('chinese')
calculate_average_score('math')
calculate_average_score('english')
delete_student('Alice')


