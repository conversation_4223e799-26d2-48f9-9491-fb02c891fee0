# practice
# NOTE STL:sandard template library C++标准模板库; Containers 容器：vector、list、map、set; Lterators 迭代器
# n=1e3，可能考虑O(n2) 或O(n2logn)的算法
# n=1e5，可能考虑O(n)或O(nlogn)的算法
# n=1e6，可能考虑O(n)或小常数O(nlogn)的算法
# n=1e7，可能考虑O(n)的算法
# n=1e14，可能考虑O(√n)的算法
# *sort
# data_list=[
#     [1,1],[2,3],[4,5],[3,2],[4,1]
# ]
# for num in data_list:
#     print(f"{num[0]} {num[1]}/",end=' ')
# data_list.sort(key=lambda d:(d[0],d[1]))
# print("\n")
# for num in data_list:
#     print(f"{num[0]} {num[1]}/",end=' ')
# *dict
# my_dict={"apple":1,"banana":2,"cherry":3}
# print(my_dict)
# print(my_dict["apple"])#输出是1
# my_dict["orange"]=2#可以是123，新添加就可以
# my_dict["apple"]=10#对已有的进行修改
# print(my_dict)
# #遍历键
# for key in my_dict:
#     print(key)
# #遍历值
# for value in my_dict.values():
#     print(value)
# #遍历键值对
# for key,value in my_dict.items():
#     print(f"{key}: {value}")
# if "banana" in my_dict:
#     print("Banana exists!")
# #删除元素
# del my_dict["cherry"]
# print(my_dict)
# *dict practice
# 1
# scores = {
#     "Tom": [90, 85, 88],
#     "Jerry": [78, 81, 86],
#     "Lucy": [92, 90, 95]
# }
# avg_scores = {}
# for key in scores:
#     avg_scores[key] = sum(scores[key])/len(scores[key])
#     print(f"{key}: {avg_scores[key]:.2f}")

# # 找出平均分最高的学生
# best_student = max(avg_scores, key=avg_scores.get)#get得到value
# print(f"The best student is {best_student}, score is {avg_scores[best_student]:.2f}")
# 统计一段英文文本中每个单词出现的次数，并按出现次数从高到低输出前3个单词及其次数。
# text = "this is a test. This test is only a test. Test test test."
# #2
# # 1. 预处理文本，全部转小写，去除标点
# import re#*正则表达式
# words = re.findall(r'\b\w+\b', text.lower())
# #*re.findall(pattern,string,flags=0)
# #*r前缀表示一个原始字符串，\b单词边界符，\w单词字符，+一次或多次
# # 2. 统计单词出现次数
# word_count = {}
# for word in words:
#     word_count[word] = word_count.get(word, 0) + 1

# # 3. 按出现次数排序并输出前3个
# top3=sorted(word_count.items(),key=lambda x:x[1],reverse=True)[:3]
# for word,count in top3:
#     print(f"{word}:{count}")
