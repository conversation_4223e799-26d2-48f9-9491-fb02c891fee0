# Ask user's name
# name = input("What's your name?")
# NOTEname=name.strip()#去掉用户字符串前后的空格
# NOTEname=name.capitalize()#首字母大写
# NOTEname=name.title()#标题式的各个首字母大写
name=input("What's your name?").strip().title()
# divide usr's first name and last name
first,last,second = name.split("e")#NOTEsplit 后的量默认是""，意思是删去""中的然后将剩余的分为几部分
# say hello to user
print(f"Hello, {first}")   
print(f"Hello, {last}")   
print(f"Hello, {second}")   
    