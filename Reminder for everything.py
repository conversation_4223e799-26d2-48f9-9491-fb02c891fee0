#NOTE python中常用的快捷键
# ctrl + z 返回上一步
# ctrl + y 重做上一步
# ctrl + x 剪切
# ctrl + c 复制
# ctrl + v 粘贴
# ctrl + a 全选
# ctrl + f 搜索
# ctrl + h 替换
# ctrl + d 删除当前行
# ctrl + shift + d 复制当前行
# ctrl + shift + j 合并行
# ctrl + shift + k 切割行
# ctrl + shift + up 向上移动行
# ctrl + shift + down 向下移动行
# ctrl + shift + enter 插入新行
# ctrl + shift + backspace 删除上一行
# ctrl + shift + tab 向左缩进
# ctrl + tab 向右缩进
# ctrl + enter 另起一行
# ctrl + backspace 删除前一个字符
# ctrl + delete 删除后一个字符
# ctrl + / 注释当前行
# ctrl + \ 视图增加
#Better Comments插件的快捷键
#TODO 待办事项
#*强调/重点
#?问题/疑问
#!警告/注意
#NOTE 备注/说明


