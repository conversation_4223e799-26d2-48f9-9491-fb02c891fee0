# 给你一个正整数 days，表示员工可工作的总天数（从第 1 天开始）。另给你一个二维数组 meetings，长度为 n，其中 meetings[i] = [start_i, end_i] 表示第 i 次会议的开始和结束天数（包含首尾）。
# 返回员工可工作且没有安排会议的天数。
# 注意：会议时间可能会有重叠。
#NOTE1 先根据meetings里面每一项的start i从小到大排序得到新的meetings的list，然后遍历前一个的end_i和后一个的start_i比大小，
#NOTE1 若后一个start_i比前一个end_i小，合并为一个新的元素（前一个的start_i后一个的end_i组成的），最后得到没有重叠的数组，让天数减去两次的时间段长度即可。
# Check if two intervals overlap（检查两个区间是否重叠）
# Merge overlapping intervals（合并重叠区间）
# Find non-overlapping intervals（找出无重叠区间）
# Calculate free slots between intervals
#*计算区间之间的空闲时间
#1
class Solution(object):
    def countDays(self, days, meetings):
        """
        :type days: int
        :type meetings: List[List[int]]
        :rtype: int
        """
        if not meetings:
            return days
        
        # Sort meetings based on start day根据会议时间的起始时间进行小到大排序
        meetings.sort(key=lambda x:x[0])
        #NOTE重温key=lambda x:对x进行操作的写法，这里的x代表list中的一个元素（数、数组均可），可以用任意字母表示
        # Merge overlapping intervals
        merged = [meetings[0]]#初始化放第一个时间段
        for current in meetings[1:]:#相比于 for i in range(1,length(meetings))更简洁
            last = merged[-1]#取merge的最后一个
            if current[0] <= last[1]:
                # Overlapping, merge them
                merged[-1] = [last[0], max(last[1], current[1])]
            else:
                merged.append(current)
        
        # Calculate total meeting days
        meeting_days = 0
        for interval in merged:
            meeting_days += interval[1] - interval[0] + 1
        
        # Return remaining work days
        return max(0, days - meeting_days)