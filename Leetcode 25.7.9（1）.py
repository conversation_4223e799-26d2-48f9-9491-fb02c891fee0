# 给你一个整数 eventTime 表示一个活动的总时长，这个活动开始于 t = 0 ，结束于 t = eventTime 。
# 同时给你两个长度为 n 的整数数组 startTime 和 endTime 。它们表示这次活动中 n 个时间 没有重叠 的会议，其中第 i 个会议的时间为 [startTime[i], endTime[i]] 。
# 你可以重新安排 至多 k 个会议，安排的规则是将会议时间平移，且保持原来的 会议时长 ，你的目的是移动会议后 最大化 相邻两个会议之间的 最长 连续空余时间。
# 移动前后所有会议之间的 相对 顺序需要保持不变，而且会议时间也需要保持互不重叠。
# 请你返回重新安排会议以后，可以得到的 最大 空余时间。
# 注意，会议 不能 安排到整个活动的时间以外。
#NOTE 中 思路就是先找到所有的相邻的空隙，根据可移动数对间隙进行k+1的求和，在所有求和中取最大值。vector动态数组特性，push_back自动添加到后面；先初始最大窗口，然后向右加一个减最前面的，取最大
#*定长滑动窗口
class Solution:
    def maxFreeTime(self, eventTime: int, k: int, startTime: list[int], endTime: list[int]) -> int:
        n = len(startTime)
        if n == 0:
            return eventTime
        
        # 计算原始间隙（注意：第一个间隙是 startTime[0] - 0）
        gaps = []
        gaps.append(startTime[0])
        for i in range(1, n):
            gaps.append(startTime[i] - endTime[i-1])
        gaps.append(eventTime - endTime[-1])  # 最后一个间隙
        
        # 滑动窗口找最大k+1个连续间隙和
        max_sum = 0
        current_sum = 0
        for i in range(len(gaps)):
            if i < k + 1:
                current_sum += gaps[i]
                max_sum = current_sum#*这里先初始化最左侧能最大移动的
            else:
                current_sum += gaps[i] - gaps[i - (k + 1)]
                max_sum = max(max_sum, current_sum)#*这里相当于向右平移
        return max_sum